import React from 'react'
import { TemplateComponent } from '../../types/templateEditor'
import { X, Eye, EyeOff, ChevronDown, ChevronRight } from 'lucide-react'

interface ComponentTreePanelProps {
  components: TemplateComponent[]
  selectedComponent: string | null
  onSelect: (id: string | null) => void
  onClose: () => void
}

export const ComponentTreePanel: React.FC<ComponentTreePanelProps> = ({
  components,
  selectedComponent,
  onSelect,
  onClose
}) => {
  const [expandedComponents, setExpandedComponents] = React.useState<Set<string>>(new Set())

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedComponents)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedComponents(newExpanded)
  }

  const renderComponent = (component: TemplateComponent, level = 0) => {
    const hasChildren = component.children && component.children.length > 0
    const isExpanded = expandedComponents.has(component.id)
    const isSelected = selectedComponent === component.id

    return (
      <div key={component.id} className="select-none">
        <div
          className={`flex items-center py-1 px-2 cursor-pointer hover:bg-gray-100 ${
            isSelected ? 'bg-blue-100 text-blue-900' : ''
          }`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={() => onSelect(component.id)}
        >
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                toggleExpanded(component.id)
              }}
              className="mr-1 p-0.5 hover:bg-gray-200 rounded"
            >
              {isExpanded ? (
                <ChevronDown className="w-3 h-3" />
              ) : (
                <ChevronRight className="w-3 h-3" />
              )}
            </button>
          )}
          
          {!hasChildren && <div className="w-4 mr-1" />}
          
          <div className="flex-1 flex items-center justify-between">
            <span className="text-sm truncate">
              {component.type} {component.content?.text && `- "${component.content.text.substring(0, 20)}..."`}
            </span>
            
            <button
              onClick={(e) => {
                e.stopPropagation()
                // Toggle visibility
              }}
              className="ml-2 p-0.5 hover:bg-gray-200 rounded opacity-60 hover:opacity-100"
            >
              <Eye className="w-3 h-3" />
            </button>
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div>
            {component.children!.map(child => renderComponent(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-900">Components</h3>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      
      <div className="flex-1 overflow-auto">
        {components.length === 0 ? (
          <div className="p-4 text-center text-gray-500 text-sm">
            No components yet. Drag components from the library to get started.
          </div>
        ) : (
          <div className="py-2">
            {components.map(component => renderComponent(component))}
          </div>
        )}
      </div>
    </div>
  )
}
