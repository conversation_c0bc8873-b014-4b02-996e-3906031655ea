import React, { useRef } from 'react'
import { useDrop } from 'react-dnd'
import { TemplateComponent } from '../../types/templateEditor'
import { ComponentRenderer } from './ComponentRenderer'
import { GridOverlay } from './GridOverlay'

interface TemplateEditorCanvasProps {
  components: TemplateComponent[]
  selectedComponent: string | null
  onComponentSelect: (id: string | null) => void
  onComponentUpdate: (id: string, updates: Partial<TemplateComponent>) => void
  onComponentAdd: (component: TemplateComponent) => void
  onComponentDelete: (id: string) => void
  zoom: number
  showGrid: boolean
  previewMode: 'desktop' | 'tablet' | 'mobile'
}

export const TemplateEditorCanvas: React.FC<TemplateEditorCanvasProps> = ({
  components,
  selectedComponent,
  onComponentSelect,
  onComponentUpdate,
  onComponentAdd,
  onComponentDelete,
  zoom,
  showGrid,
  previewMode
}) => {
  const canvasRef = useRef<HTMLDivElement>(null)

  const [{ isOver }, drop] = useDrop({
    accept: ['component'],
    drop: (item: any, monitor) => {
      if (!canvasRef.current) return

      const canvasRect = canvasRef.current.getBoundingClientRect()
      const offset = monitor.getClientOffset()

      if (offset) {
        const x = (offset.x - canvasRect.left) / zoom
        const y = (offset.y - canvasRect.top) / zoom

        // Adding new component from library
        const newComponent: TemplateComponent = {
          id: `component-${Date.now()}`,
          type: item.componentType,
          content: item.defaultContent || {},
          styles: {
            ...item.defaultStyles,
            position: 'absolute',
            left: x,
            top: y,
            zIndex: components.length + 1
          },
          position: { x, y }
        }
        onComponentAdd(newComponent)
        onComponentSelect(newComponent.id)
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  })

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      onComponentSelect(null)
    }
  }

  // Canvas dimensions based on preview mode
  const getCanvasWidth = () => {
    switch (previewMode) {
      case 'mobile': return 375
      case 'tablet': return 768
      case 'desktop': return 1200
      default: return 1200
    }
  }

  // Combine the drop ref with the canvas ref
  const combinedRef = (node: HTMLDivElement) => {
    canvasRef.current = node
    drop(node)
  }

  return (
    <div className="relative flex-1 overflow-hidden bg-gray-100">
      <div className="flex-1 overflow-auto flex justify-center">
        <div
          ref={combinedRef}
          className={`relative bg-white shadow-lg ${
            isOver ? 'ring-2 ring-blue-500' : ''
          }`}
          style={{
            width: getCanvasWidth(),
            minHeight: 800,
            transform: `scale(${zoom})`,
            transformOrigin: 'top center'
          }}
          onClick={handleCanvasClick}
        >
          {showGrid && <GridOverlay size={20} />}

          {components.map((component) => (
            <div
              key={component.id}
              style={{
                position: 'absolute',
                left: component.position?.x || 0,
                top: component.position?.y || 0,
                zIndex: component.styles?.zIndex || 0,
                border: selectedComponent === component.id ? '2px solid #3b82f6' : 'none',
                cursor: 'pointer'
              }}
              onClick={(e) => {
                e.stopPropagation()
                onComponentSelect(component.id)
              }}
            >
              <ComponentRenderer
                component={component}
                isSelected={selectedComponent === component.id}
                onUpdate={(updates) => onComponentUpdate(component.id, updates)}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}