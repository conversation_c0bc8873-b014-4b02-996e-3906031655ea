import React from 'react'
import { TemplateComponent } from '../../types/templateEditor'
import { X, Type, Palette, Layout, Image } from 'lucide-react'

interface PropertyPanelProps {
  component: TemplateComponent | null
  onUpdate: (updates: Partial<TemplateComponent>) => void
  onClose: () => void
}

export const PropertyPanel: React.FC<PropertyPanelProps> = ({
  component,
  onUpdate,
  onClose
}) => {
  if (!component) {
    return (
      <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
        <div className="flex items-center justify-between p-3 border-b border-gray-200">
          <h3 className="text-sm font-medium text-gray-900">Properties</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        <div className="flex-1 flex items-center justify-center text-gray-500 text-sm">
          Select a component to edit its properties
        </div>
      </div>
    )
  }

  const updateContent = (updates: Record<string, any>) => {
    onUpdate({
      content: {
        ...component.content,
        ...updates
      }
    })
  }

  const updateStyles = (updates: Record<string, any>) => {
    onUpdate({
      styles: {
        ...component.styles,
        ...updates
      }
    })
  }

  const renderContentProperties = () => {
    switch (component.type) {
      case 'text':
      case 'heading':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Text Content
              </label>
              <textarea
                value={component.content?.text || ''}
                onChange={(e) => updateContent({ text: e.target.value })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded resize-none"
                rows={3}
              />
            </div>
            {component.type === 'heading' && (
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Heading Level
                </label>
                <select
                  value={component.content?.level || 'h2'}
                  onChange={(e) => updateContent({ level: e.target.value })}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                >
                  <option value="h1">H1</option>
                  <option value="h2">H2</option>
                  <option value="h3">H3</option>
                  <option value="h4">H4</option>
                  <option value="h5">H5</option>
                  <option value="h6">H6</option>
                </select>
              </div>
            )}
          </div>
        )

      case 'image':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Image URL
              </label>
              <input
                type="url"
                value={component.content?.src || ''}
                onChange={(e) => updateContent({ src: e.target.value })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                placeholder="https://example.com/image.jpg"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Alt Text
              </label>
              <input
                type="text"
                value={component.content?.alt || ''}
                onChange={(e) => updateContent({ alt: e.target.value })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                placeholder="Image description"
              />
            </div>
          </div>
        )

      case 'button':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Button Text
              </label>
              <input
                type="text"
                value={component.content?.text || ''}
                onChange={(e) => updateContent({ text: e.target.value })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                placeholder="Click here"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Link URL
              </label>
              <input
                type="url"
                value={component.content?.href || ''}
                onChange={(e) => updateContent({ href: e.target.value })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                placeholder="https://example.com"
              />
            </div>
          </div>
        )

      default:
        return (
          <div className="text-sm text-gray-500">
            No content properties available for this component type.
          </div>
        )
    }
  }

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-900">Properties</h3>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      
      <div className="flex-1 overflow-auto p-3 space-y-4">
        {/* Component Info */}
        <div>
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide mb-2">
            Component
          </h4>
          <div className="text-sm text-gray-600 capitalize">
            {component.type}
          </div>
        </div>

        {/* Content Properties */}
        <div>
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide mb-2 flex items-center">
            <Type className="w-3 h-3 mr-1" />
            Content
          </h4>
          {renderContentProperties()}
        </div>

        {/* Style Properties */}
        <div>
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide mb-2 flex items-center">
            <Palette className="w-3 h-3 mr-1" />
            Appearance
          </h4>
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Width
                </label>
                <input
                  type="text"
                  value={component.styles?.width || 'auto'}
                  onChange={(e) => updateStyles({ width: e.target.value })}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                  placeholder="auto"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Height
                </label>
                <input
                  type="text"
                  value={component.styles?.height || 'auto'}
                  onChange={(e) => updateStyles({ height: e.target.value })}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                  placeholder="auto"
                />
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Background Color
              </label>
              <input
                type="color"
                value={component.styles?.backgroundColor || '#ffffff'}
                onChange={(e) => updateStyles({ backgroundColor: e.target.value })}
                className="w-full h-8 border border-gray-300 rounded"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Text Color
              </label>
              <input
                type="color"
                value={component.styles?.color || '#000000'}
                onChange={(e) => updateStyles({ color: e.target.value })}
                className="w-full h-8 border border-gray-300 rounded"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Padding
              </label>
              <input
                type="text"
                value={component.styles?.padding || '0'}
                onChange={(e) => updateStyles({ padding: e.target.value })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                placeholder="10px"
              />
            </div>
          </div>
        </div>

        {/* Position Properties */}
        <div>
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide mb-2 flex items-center">
            <Layout className="w-3 h-3 mr-1" />
            Position
          </h4>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                X Position
              </label>
              <input
                type="number"
                value={component.position?.x || 0}
                onChange={(e) => onUpdate({
                  position: { ...component.position, x: parseInt(e.target.value) || 0 }
                })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Y Position
              </label>
              <input
                type="number"
                value={component.position?.y || 0}
                onChange={(e) => onUpdate({
                  position: { ...component.position, y: parseInt(e.target.value) || 0 }
                })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
